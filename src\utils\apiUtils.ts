// Utility functions for API configuration and validation

/**
 * Check if API endpoint is properly configured
 */
export const isApiConfigured = (): boolean => {
  const endpoint = import.meta.env.DEV
    ? (import.meta.env.VITE_API_BASE_URL ? '/api' : '')
    : (import.meta.env.VITE_API_BASE_URL || '');
    
  return endpoint !== null && endpoint !== undefined && endpoint.trim() !== '';
};

/**
 * Get the current API endpoint
 */
export const getApiEndpoint = (): string => {
  return import.meta.env.DEV
    ? (import.meta.env.VITE_API_BASE_URL ? '/api' : '')
    : (import.meta.env.VITE_API_BASE_URL || '');
};

/**
 * Show user-friendly message when API is not configured
 */
export const showApiNotConfiguredMessage = (): void => {
  console.warn('API endpoint is not configured. Please set VITE_API_BASE_URL in your .env file.');
};

/**
 * Validate endpoint before making API calls
 */
export const validateEndpoint = (endpoint: string): boolean => {
  if (!endpoint || endpoint.trim() === '') {
    showApiNotConfiguredMessage();
    return false;
  }
  return true;
};
