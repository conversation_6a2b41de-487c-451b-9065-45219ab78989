{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:analyze": "vite build --mode analyze", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@types/styled-components": "^5.1.34", "clsx": "^2.1.1", "dotenv": "^17.2.0", "lucide-react": "^0.344.0", "motion": "^12.23.6", "nuqs": "^2.4.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-markdown": "^10.1.0", "sonner": "^2.0.6", "styled-components": "^6.1.19", "tailwind-merge": "^3.3.1", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "cssnano": "^7.1.0", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "rollup-plugin-visualizer": "^6.0.3", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}